#!/usr/bin/env node

import axios from 'axios';

// Test script to verify that our endpoint fixes work correctly
async function testEndpoints() {
  console.log('Testing key endpoints for HTTPS redirect issues...\n');

  // Create axios instance with HTTPS-strict configuration
  const apiClient = axios.create({
    baseURL: 'https://api.isms.helevon.org/api/v1',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'ISMS-Desktop-App/1.0',
      'Cache-Control': 'no-cache',
    },
    maxRedirects: 0, // Prevent automatic redirects
    validateStatus: (status) => status >= 200 && status < 400, // Accept redirects for testing
  });

  // Add response interceptor to catch redirects
  apiClient.interceptors.response.use(
    (response) => {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} → ${response.status} ${response.statusText}`);
      if (response.status >= 300 && response.status < 400) {
        console.log(`🔄 Redirect to: ${response.headers.location || 'Unknown'}`);
      }
      return response;
    },
    (error) => {
      const method = error.config?.method?.toUpperCase() || 'UNKNOWN';
      const url = error.config?.url || 'unknown';
      console.log(`❌ ${method} ${url} → ${error.response?.status || 'Network Error'} ${error.response?.statusText || error.message}`);
      if (error.response?.status >= 300 && error.response?.status < 400) {
        console.log(`🔄 Redirect to: ${error.response.headers?.location || 'Unknown'}`);
      }
      return Promise.reject(error);
    }
  );

  // Test key endpoints that are used after login
  const endpoints = [
    { method: 'GET', url: '/inventory/products', description: 'Get all products' },
    { method: 'GET', url: '/sales/orders', description: 'Get all orders' },
    { method: 'GET', url: '/inventory/suppliers', description: 'Get all suppliers' },
    { method: 'GET', url: '/users', description: 'Get all users' },
    { method: 'GET', url: '/sales/reports/daily-sales', description: 'Get daily sales report' },
    { method: 'GET', url: '/stock-movements', description: 'Get stock movements' },
  ];

  console.log('Testing endpoints without trailing slashes (should work):');
  console.log('=' .repeat(60));

  for (const endpoint of endpoints) {
    try {
      await apiClient.request({
        method: endpoint.method,
        url: endpoint.url,
      });
    } catch (error) {
      // Expected to fail with 401/403, but should not redirect
    }
  }

  console.log('\n' + '=' .repeat(60));
  console.log('Summary: If you see any 307/308 redirects above, those endpoints still have issues.');
  console.log('All endpoints should return 401 Unauthorized (expected without auth token).');
  console.log('No endpoints should show redirects to HTTP URLs.');
}

// Run the test
testEndpoints().catch(console.error);
